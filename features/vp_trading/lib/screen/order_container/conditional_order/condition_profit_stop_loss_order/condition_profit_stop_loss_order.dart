import 'package:flutter/material.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';

void opendEditConditionProfitStopLossOrderBottomSheet({
  required BuildContext context,
  required ConditionOrderBookModel item,
  required VoidCallback onEditSuccess,
}) {
  VPPopup.bottomSheet(
    _EditConditionProfitStopLossOrder(item: item),
  ).showSheet(context);
}

class _EditConditionProfitStopLossOrder extends StatelessWidget {
  const _EditConditionProfitStopLossOrder({super.key, required this.item});
  final ConditionOrderBookModel item;
  @override
  Widget build(BuildContext context) {
    return Container();
  }
}
